/* === Design PARFAITEMENT IDENTIQUE à l'image === */
:root {
    /* Couleurs EXACTES de l'image */
    --primary-color: #1e3a8a; /* Bleu foncé du bouton SIGN IN */
    --primary-dark: #1e40af;
    --accent-color: #f97316; /* Orange de la bordure */

    /* Texte EXACT */
    --text-color: #1f2937;
    --text-light: #6b7280;
    --text-muted: #9ca3af;
    --text-placeholder: #9ca3af;

    /* Arrière-plan EXACT de l'image */
    --bg-color: #e2e8f0; /* Gris bleuté comme dans l'image */
    --logo-bg: #e2e8f0; /* Même gris pour la section logo */

    /* Couleurs de base */
    --white: #ffffff;
    --border-color: #e5e7eb;
    --input-border: #e5e7eb;

    /* Ombres EXACTES de l'image */
    --shadow-card: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* États */
    --error-color: #ef4444;
    --success-color: #10b981;
    --google-color: #ea4335;
    --github-color: #24292e;

    /* Transitions */
    --transition: 0.2s ease;
}

/* === Reset et styles de base === */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

body {
    background: var(--bg-color);
    color: var(--text-color);
    min-height: 100vh;
    font-weight: 400;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: relative;
    overflow-x: hidden;
}

/* === Cercles décoratifs orange et bleus en arrière-plan === */
.bg-circle {
    position: fixed;
    border-radius: 50%;
    z-index: -1;
    opacity: 0.7;
    filter: blur(60px);
    animation: floatCircle 25s ease-in-out infinite;
}

/* Cercles Bleus */
.bg-circle-1 {
    width: 450px;
    height: 450px;
    background: radial-gradient(circle, rgba(30, 58, 138, 0.4) 0%, rgba(30, 58, 138, 0.15) 50%, transparent 100%);
    top: -250px;
    right: -250px;
    animation-delay: 0s;
}

.bg-circle-2 {
    width: 320px;
    height: 320px;
    background: radial-gradient(circle, rgba(30, 58, 138, 0.3) 0%, rgba(30, 58, 138, 0.1) 50%, transparent 100%);
    top: 15%;
    left: -160px;
    animation: floatCircleReverse 30s ease-in-out infinite;
    animation-delay: 8s;
}

.bg-circle-3 {
    width: 280px;
    height: 280px;
    background: radial-gradient(circle, rgba(30, 58, 138, 0.25) 0%, rgba(30, 58, 138, 0.08) 50%, transparent 100%);
    bottom: 20%;
    right: -140px;
    animation: floatCircle 25s ease-in-out infinite;
    animation-delay: 16s;
}

/* Cercles Orange */
.bg-circle-4 {
    width: 380px;
    height: 380px;
    background: radial-gradient(circle, rgba(249, 115, 22, 0.4) 0%, rgba(249, 115, 22, 0.15) 50%, transparent 100%);
    bottom: -200px;
    left: -200px;
    animation: floatCircleReverse 28s ease-in-out infinite;
    animation-delay: 4s;
}

.bg-circle-5 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, rgba(249, 115, 22, 0.3) 0%, rgba(249, 115, 22, 0.1) 50%, transparent 100%);
    top: 40%;
    right: -125px;
    animation-delay: 12s;
}

.bg-circle-6 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(249, 115, 22, 0.25) 0%, rgba(249, 115, 22, 0.08) 50%, transparent 100%);
    top: 70%;
    left: -100px;
    animation: floatCircleReverse 32s ease-in-out infinite;
    animation-delay: 20s;
}

/* Cercles supplémentaires pour plus d'effet */
.bg-circle-7 {
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(30, 58, 138, 0.2) 0%, rgba(30, 58, 138, 0.05) 50%, transparent 100%);
    top: 5%;
    left: 20%;
    animation-delay: 6s;
}

.bg-circle-8 {
    width: 180px;
    height: 180px;
    background: radial-gradient(circle, rgba(249, 115, 22, 0.2) 0%, rgba(249, 115, 22, 0.05) 50%, transparent 100%);
    bottom: 10%;
    right: 15%;
    animation: floatCircleReverse 26s ease-in-out infinite;
    animation-delay: 14s;
}

/* === Wrapper principal === */
.login-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 2rem;
    background: var(--bg-color);
}

/* === Container PARFAITEMENT IDENTIQUE à l'image === */
.login-container {
    max-width: 900px;
    width: 100%;
    background: var(--white);
    border-radius: 20px;
    box-shadow: var(--shadow-card);
    overflow: hidden;
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 550px;
    transition: var(--transition);
    position: relative;
}

/* === Section Logo PARFAITEMENT IDENTIQUE à l'image === */
.logo-section {
    background: var(--logo-bg);
    padding: 3rem 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
}

/* Bordure orange en bas EXACTE comme dans l'image */
.logo-section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #1e3a8a 0%, var(--accent-color) 100%);
}

/* Logo circle PARFAITEMENT IDENTIQUE */
.logo-circle {
    width: 120px;
    height: 120px;
    background: var(--white);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 3px solid #cbd5e1;
}

.logo-image {
    width: 70px;
    height: auto;
    max-width: 100%;
}

/* Company name EXACT */
.company-name {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 0.75rem;
    letter-spacing: -0.025em;
}

/* Welcome text EXACT */
.welcome-text {
    font-size: 1rem;
    color: var(--text-light);
    font-weight: 400;
}

/* === Section Formulaire PARFAITEMENT IDENTIQUE à l'image === */
.form-section {
    padding: 3rem 2.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: var(--white);
}

.form-content {
    max-width: 350px;
    margin: 0 auto;
    width: 100%;
}

/* Titre PARFAITEMENT IDENTIQUE à l'image */
.title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-color);
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
}

.title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 3px;
    background: var(--accent-color);
    border-radius: 2px;
}

/* Sous-titre pour forgot password */
.subtitle {
    text-align: center;
    font-size: 0.875rem;
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

/* Messages de succès */
.success-message {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    border-left: 4px solid var(--success-color);
    color: #166534;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
}

/* === Champs de formulaire PARFAITEMENT IDENTIQUES à l'image === */
.form-field {
    margin-bottom: 1.25rem;
}

.form-field label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.875rem;
}

.password-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.forgot-password-link {
    font-size: 0.875rem;
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition);
}

.forgot-password-link:hover {
    color: var(--primary-color);
}

/* Input wrapper PARFAITEMENT IDENTIQUE à l'image */
.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    z-index: 1;
    font-size: 1.125rem;
}

/* Inputs PARFAITEMENT IDENTIQUES à l'image */
input[type="email"],
input[type="password"],
input[type="text"] {
    width: 100%;
    padding: 16px 16px 16px 48px;
    border: 2px solid var(--input-border);
    border-radius: 12px;
    font-size: 1rem;
    color: var(--text-color);
    background: var(--white);
    transition: var(--transition);
    font-weight: 400;
}

input[type="email"]:focus,
input[type="password"]:focus,
input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

input::placeholder {
    color: var(--text-placeholder);
    font-weight: 400;
    font-size: 1rem;
}

.toggle-password {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-light);
    z-index: 2;
    padding: 0;
    font-size: 1.125rem;
    transition: var(--transition);
}

.toggle-password:hover {
    color: var(--primary-color);
}

/* === Bouton PARFAITEMENT IDENTIQUE à l'image === */
.signin-btn {
    width: 100%;
    padding: 16px 24px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 700;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: var(--transition);
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 14px rgba(30, 58, 138, 0.3);
}

.signin-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(30, 58, 138, 0.4);
}

.signin-btn:active {
    transform: translateY(0);
}

/* === Divider PARFAITEMENT IDENTIQUE à l'image === */
.divider-text {
    text-align: center;
    margin: 1.5rem 0;
    color: var(--text-light);
    font-size: 0.875rem;
    position: relative;
}

.divider-text::before,
.divider-text::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 40%;
    height: 1px;
    background: var(--border-color);
}

.divider-text::before {
    left: 0;
}

.divider-text::after {
    right: 0;
}

/* === Boutons sociaux PARFAITEMENT IDENTIQUES à l'image === */
.social-signup {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 12px 20px;
    background: var(--white);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    text-decoration: none;
    color: var(--text-color);
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition);
    min-width: 120px;
}

.social-btn:hover {
    border-color: var(--primary-color);
    background: #f8fafc;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.social-btn img {
    width: 20px;
    height: 20px;
}

/* === Lien d'inscription PARFAITEMENT IDENTIQUE à l'image === */
.signup-link {
    text-align: center;
    font-size: 0.875rem;
    color: var(--text-light);
    margin-bottom: 1rem;
}

.signup-link a {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
}

.signup-link a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* === Copyright PARFAITEMENT IDENTIQUE à l'image === */
.copyright {
    text-align: center;
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* === Messages d'erreur PARFAITEMENT IDENTIQUES à l'image === */
.error-message {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-left: 4px solid var(--error-color);
    color: #dc2626;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
}

/* === Responsive EXACT comme dans l'image === */
@media (max-width: 768px) {
    .login-container {
        grid-template-columns: 1fr;
        max-width: 400px;
        min-height: auto;
    }

    .logo-section {
        padding: 2rem 1.5rem;
        border-bottom: 3px solid var(--accent-color);
        border-right: none;
    }

    .form-section {
        padding: 2rem 1.5rem;
    }

    .logo-circle {
        width: 80px;
        height: 80px;
        margin-bottom: 1rem;
    }

    .logo-image {
        width: 50px;
    }

    .company-name {
        font-size: 1.25rem;
    }

    .title {
        font-size: 1.25rem;
    }

    .form-content {
        max-width: 280px;
    }
}

@media (max-width: 480px) {
    .login-wrapper {
        padding: 1rem;
    }

    .login-container {
        border-radius: 12px;
        max-width: 350px;
    }

    .form-section {
        padding: 1.5rem 1rem;
    }

    .logo-section {
        padding: 1.5rem 1rem;
    }

    .social-signup {
        flex-direction: column;
        gap: 0.5rem;
    }

    .social-btn {
        width: 100%;
        min-width: auto;
    }

    .form-content {
        max-width: 260px;
    }
}

/* === Animations élégantes pour les cercles === */
@keyframes floatCircle {
    0% {
        transform: translateY(0px) translateX(0px) scale(1) rotate(0deg);
        opacity: 0.7;
    }
    20% {
        transform: translateY(-30px) translateX(20px) scale(1.1) rotate(72deg);
        opacity: 0.8;
    }
    40% {
        transform: translateY(-15px) translateX(-25px) scale(0.9) rotate(144deg);
        opacity: 0.6;
    }
    60% {
        transform: translateY(-40px) translateX(15px) scale(1.05) rotate(216deg);
        opacity: 0.75;
    }
    80% {
        transform: translateY(-10px) translateX(-10px) scale(0.95) rotate(288deg);
        opacity: 0.65;
    }
    100% {
        transform: translateY(0px) translateX(0px) scale(1) rotate(360deg);
        opacity: 0.7;
    }
}

/* Animation alternative pour certains cercles */
@keyframes floatCircleReverse {
    0% {
        transform: translateY(0px) translateX(0px) scale(1) rotate(360deg);
        opacity: 0.6;
    }
    25% {
        transform: translateY(25px) translateX(-15px) scale(1.08) rotate(270deg);
        opacity: 0.8;
    }
    50% {
        transform: translateY(10px) translateX(20px) scale(0.92) rotate(180deg);
        opacity: 0.5;
    }
    75% {
        transform: translateY(35px) translateX(-8px) scale(1.03) rotate(90deg);
        opacity: 0.7;
    }
    100% {
        transform: translateY(0px) translateX(0px) scale(1) rotate(0deg);
        opacity: 0.6;
    }
}
