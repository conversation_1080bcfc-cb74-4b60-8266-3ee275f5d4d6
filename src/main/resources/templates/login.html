<!DOCTYPE html>
<html lang="fr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign In - Kairos IT</title>
    <link rel="stylesheet" th:href="@{/css/reference-login-style.css}">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
</head>

<body>
<!-- Cercles décoratifs orange et bleus en arrière-plan -->
<div class="bg-circle bg-circle-1"></div>
<div class="bg-circle bg-circle-2"></div>
<div class="bg-circle bg-circle-3"></div>
<div class="bg-circle bg-circle-4"></div>
<div class="bg-circle bg-circle-5"></div>
<div class="bg-circle bg-circle-6"></div>
<div class="bg-circle bg-circle-7"></div>
<div class="bg-circle bg-circle-8"></div>

<div class="login-wrapper">
    <div class="login-container">
        <!-- Logo Section (Left) -->
        <div class="logo-section">
            <div class="logo-circle">
                <img src="/assets/images/logo.png" alt="Kairos IT Logo" class="logo-image" />
            </div>

            <h1 class="company-name">Kairos IT</h1>

            <p class="welcome-text">Welcome to sign in to Kairos IT</p>
        </div>

        <!-- Form Section (Right) -->
        <div class="form-section">
            <form th:action="@{/login}" method="post" class="login-form">
                <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}">

                <div class="form-content">
                    <h2 class="title">Sign In</h2>

                    <!-- Error Messages -->
                    <div class="error-message" th:if="${param.error}">
                        <p th:if="${session.SPRING_SECURITY_LAST_EXCEPTION != null and session.SPRING_SECURITY_LAST_EXCEPTION.message.contains('UserNotFound')}">
                            Email does not exist. <a href="http://localhost:4200/auth/sign-up">Sign up here</a>.
                        </p>
                        <p th:if="${session.SPRING_SECURITY_LAST_EXCEPTION != null and session.SPRING_SECURITY_LAST_EXCEPTION.message.contains('BadCredentials')}">
                            Incorrect password. Please try again.
                        </p>
                        <p th:if="${session.SPRING_SECURITY_LAST_EXCEPTION != null and !session.SPRING_SECURITY_LAST_EXCEPTION.message.contains('UserNotFound') and !session.SPRING_SECURITY_LAST_EXCEPTION.message.contains('BadCredentials')}">
                            Invalid login credentials. Please try again.
                        </p>
                    </div>

                        <!-- Success Message for Password Reset -->
                        <div class="success-message" th:if="${param.passwordReset}">
                            <p>Your password has been reset successfully. Please login with your new password.</p>
                        </div>

                    <!-- Email Field -->
                    <div class="form-field">
                        <label for="email">Email</label>
                        <div class="input-wrapper">
                            <span class="material-icons input-icon">email</span>
                            <input type="email" name="username" id="email" placeholder="Enter your email" required>
                        </div>
                    </div>

                    <!-- Password Field -->
                    <div class="form-field">
                        <div class="password-header">
                            <label for="password">Password</label>
                            <a href="http://localhost:8080/forgot-password" class="forgot-password-link">Forgot Password?</a>
                        </div>
                        <div class="input-wrapper">
                            <span class="material-icons input-icon">lock</span>
                            <input type="password" name="password" id="password" placeholder="Enter your password" required>
                            <button type="button" class="toggle-password" onclick="toggleVisibility()" aria-label="Toggle password visibility">
                                <span class="material-icons" id="toggleIcon">visibility</span>
                            </button>
                        </div>
                    </div>

                    <!-- Sign In Button -->
                    <button type="submit" class="signin-btn" aria-label="Sign In">
                        SIGN IN
                    </button>

                    <!-- Divider -->
                    <div class="divider-text">
                        <span>Or sign in with</span>
                    </div>

                    <!-- Social Login -->
                    <div class="social-signup">
                        <a th:href="@{/oauth2/authorization/google}" class="social-btn" aria-label="Google">
                            <img src="/assets/images/google.png" alt="Google Icon">
                            <span>Google</span>
                        </a>
                        <a th:href="@{/oauth2/authorization/github}" class="social-btn" aria-label="GitHub">
                            <img src="/assets/images/github.png" alt="GitHub Icon">
                            <span>GitHub</span>
                        </a>
                    </div>

                    <!-- Sign Up Link -->
                    <div class="signup-link">
                        <span>Don't have an account? <a href="http://localhost:4200/auth/sign-up">Sign up</a></span>
                    </div>

                    <!-- Copyright -->
                    <div class="copyright">
                        <span>© 2023 Kairos IT. All rights reserved.</span>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    if (window.location.search.includes("success")) {
        window.location.href = "http://localhost:4200/profile";
    }

    // Toggle password visibility
    function toggleVisibility() {
        let passwordField = document.getElementById("password");
        let icon = document.getElementById("toggleIcon");
        if (passwordField.type === "password") {
            passwordField.type = "text";
            icon.innerText = "visibility";
        } else {
            passwordField.type = "password";
            icon.innerText = "visibility_off";
        }
    }
</script>
</body>
</html>